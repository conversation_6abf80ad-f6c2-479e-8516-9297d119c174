import React, { useState } from 'react';
import { useChat } from '../../contexts/ChatContext';
import { useAuth } from '../../contexts/AuthContext';
import { Room } from '../../types';

interface RoomListProps {
  onRoomSelect: (room: Room) => void;
}

export default function RoomList({ onRoomSelect }: RoomListProps) {
  const { rooms, currentRoom, createRoom, joinRoom, error, clearError } = useChat();
  const { logout, user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newRoomName, setNewRoomName] = useState('');
  const [newRoomDescription, setNewRoomDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleRoomSelect = async (room: Room) => {
    try {
      clearError();
      await joinRoom(room.id);
      onRoomSelect(room);
    } catch (error) {
      // Error is handled by context
    }
  };

  const handleCreateRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newRoomName.trim()) return;

    try {
      setIsCreating(true);
      clearError();
      await createRoom(newRoomName.trim(), newRoomDescription.trim() || undefined);
      setNewRoomName('');
      setNewRoomDescription('');
      setShowCreateForm(false);
    } catch (error) {
      // Error is handled by context
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="w-64 bg-gray-800 text-white flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <h1 className="text-xl font-bold">Chat Rooms</h1>
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="text-gray-400 hover:text-white"
            title="Create Room"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
        
        {user && (
          <div className="flex items-center justify-between text-sm text-gray-400">
            <span>@{user.username}</span>
            <button
              onClick={logout}
              className="hover:text-white"
              title="Logout"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Create Room Form */}
      {showCreateForm && (
        <div className="p-4 border-b border-gray-700 bg-gray-700">
          <form onSubmit={handleCreateRoom} className="space-y-3">
            <input
              type="text"
              placeholder="Room name"
              value={newRoomName}
              onChange={(e) => setNewRoomName(e.target.value)}
              className="w-full px-3 py-2 bg-gray-600 text-white rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
              maxLength={50}
              required
            />
            <input
              type="text"
              placeholder="Description (optional)"
              value={newRoomDescription}
              onChange={(e) => setNewRoomDescription(e.target.value)}
              className="w-full px-3 py-2 bg-gray-600 text-white rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
              maxLength={200}
            />
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={isCreating || !newRoomName.trim()}
                className="flex-1 py-2 px-3 bg-indigo-600 text-white rounded hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                {isCreating ? 'Creating...' : 'Create'}
              </button>
              <button
                type="button"
                onClick={() => setShowCreateForm(false)}
                className="py-2 px-3 bg-gray-600 text-white rounded hover:bg-gray-500 text-sm"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-600 text-white text-sm">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <button onClick={clearError} className="text-red-200 hover:text-white">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Room List */}
      <div className="flex-1 overflow-y-auto">
        {rooms.length === 0 ? (
          <div className="p-4 text-gray-400 text-center">
            No rooms available. Create one to get started!
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {rooms.map((room) => (
              <button
                key={room.id}
                onClick={() => handleRoomSelect(room)}
                className={`w-full text-left p-3 rounded-lg transition-colors ${
                  currentRoom?.id === room.id
                    ? 'bg-indigo-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">#{room.name}</div>
                    {room.description && (
                      <div className="text-sm text-gray-400 truncate">{room.description}</div>
                    )}
                  </div>
                  <div className="flex flex-col items-end text-xs text-gray-400 ml-2">
                    {room._count && (
                      <>
                        <span>{room._count.members} members</span>
                        <span>{room._count.messages} messages</span>
                      </>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
